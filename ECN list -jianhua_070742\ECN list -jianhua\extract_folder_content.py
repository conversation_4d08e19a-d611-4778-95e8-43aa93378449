import os
import pandas as pd
from pathlib import Path
import PyPDF2
from pptx import Presentation
import warnings
import re
warnings.filterwarnings('ignore')

def clean_text_for_excel(text):
    """清理文本中Excel不支持的字符"""
    if not text:
        return text

    # 移除或替换Excel不支持的控制字符
    # 保留常见的换行符和制表符，移除其他控制字符
    cleaned_text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

    # 限制文本长度，Excel单元格最大支持32767个字符
    if len(cleaned_text) > 32000:
        cleaned_text = cleaned_text[:32000] + "...(内容过长已截断)"

    return cleaned_text

def extract_pdf_content(pdf_path):
    """提取PDF文件的所有文本内容"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text_content = ""
            for page in pdf_reader.pages:
                text_content += page.extract_text() + "\n"
            # 清理文本中的特殊字符
            cleaned_text = clean_text_for_excel(text_content.strip())
            return cleaned_text
    except Exception as e:
        return f"PDF读取错误: {str(e)}"

def extract_pptx_page_content(pptx_path, page_number):
    """提取PPTX文件指定页的内容"""
    try:
        prs = Presentation(pptx_path)
        if len(prs.slides) < page_number:
            return ""

        # 获取指定页（索引为page_number-1）
        slide = prs.slides[page_number - 1]
        text_content = ""

        # 提取所有文本框的内容
        for shape in slide.shapes:
            if hasattr(shape, "text"):
                text_content += shape.text + "\n"

        # 清理文本中的特殊字符
        cleaned_text = clean_text_for_excel(text_content.strip())
        return cleaned_text
    except Exception as e:
        return f"PPTX第{page_number}页读取错误: {str(e)}"

def extract_pptx_second_and_third_pages(pptx_path):
    """提取PPTX文件第二页和第三页的内容"""
    try:
        prs = Presentation(pptx_path)
        if len(prs.slides) < 2:
            return "PPTX文件少于2页", ""

        # 提取第二页内容
        second_page_content = extract_pptx_page_content(pptx_path, 2)

        # 提取第三页内容（如果存在）
        third_page_content = extract_pptx_page_content(pptx_path, 3)

        return second_page_content, third_page_content
    except Exception as e:
        return f"PPTX读取错误: {str(e)}", ""

def scan_folders_and_extract():
    """扫描文件夹并提取内容"""
    # 当前目录就是"ECN list -jianhua"，扫描当前目录下的所有子文件夹
    base_path = Path(".")
    results = []

    # 遍历当前目录下的所有子文件夹
    for folder_path in base_path.iterdir():
        if folder_path.is_dir() and folder_path.name != "__pycache__":
            folder_name = folder_path.name

            # 在文件夹中查找PDF或PPTX文件
            pdf_files = list(folder_path.glob("*.pdf"))
            pptx_files = list(folder_path.glob("*.pptx"))

            # 只有找到PDF或PPTX文件才处理
            if pdf_files:
                # 如果有PDF文件，提取第一个PDF的内容
                pdf_file = pdf_files[0]
                second_page_content = extract_pdf_content(pdf_file)
                third_page_content = ""  # PDF没有第三页概念，留空
                file_type = "PDF"

                results.append({
                    "文件夹名称": folder_name,
                    "第二页内容": second_page_content,
                    "第三页内容": third_page_content,
                    "文件格式": file_type
                })
            elif pptx_files:
                # 如果有PPTX文件，提取第一个PPTX的第二页和第三页内容
                pptx_file = pptx_files[0]
                second_page_content, third_page_content = extract_pptx_second_and_third_pages(pptx_file)
                file_type = "PPTX"

                results.append({
                    "文件夹名称": folder_name,
                    "第二页内容": second_page_content,
                    "第三页内容": third_page_content,
                    "文件格式": file_type
                })
            # 如果既没有PDF也没有PPTX文件，直接跳过，不添加到结果中

    return results

def main():
    """主函数"""
    print("开始扫描文件夹...")
    results = scan_folders_and_extract()
    
    if not results:
        print("没有找到任何文件夹或内容")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(results)
    
    # 保存到Excel文件
    output_file = "文件夹内容提取结果.xlsx"
    df.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"提取完成！结果已保存到: {output_file}")
    print(f"共处理了 {len(results)} 个文件夹")
    
    # 显示前几行结果
    print("\n前几行结果预览:")
    print(df.head())

if __name__ == "__main__":
    main()
 