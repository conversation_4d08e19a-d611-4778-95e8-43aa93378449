import os
import pandas as pd
from pathlib import Path
import PyPDF2
from pptx import Presentation
import warnings
warnings.filterwarnings('ignore')

def extract_pdf_content(pdf_path):
    """提取PDF文件的所有文本内容"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text_content = ""
            for page in pdf_reader.pages:
                text_content += page.extract_text() + "\n"
            return text_content.strip()
    except Exception as e:
        return f"PDF读取错误: {str(e)}"

def extract_pptx_second_page(pptx_path):
    """提取PPTX文件第二页的内容"""
    try:
        prs = Presentation(pptx_path)
        if len(prs.slides) < 2:
            return "PPTX文件少于2页"
        
        # 获取第二页（索引为1）
        slide = prs.slides[1]
        text_content = ""
        
        # 提取所有文本框的内容
        for shape in slide.shapes:
            if hasattr(shape, "text"):
                text_content += shape.text + "\n"
        
        return text_content.strip()
    except Exception as e:
        return f"PPTX读取错误: {str(e)}"

def scan_folders_and_extract():
    """扫描文件夹并提取内容"""
    # 当前目录就是"ECN list -jianhua"，扫描当前目录下的所有子文件夹
    base_path = Path(".")
    results = []
    
    # 遍历当前目录下的所有子文件夹
    for folder_path in base_path.iterdir():
        if folder_path.is_dir() and folder_path.name != "__pycache__":
            folder_name = folder_path.name
            content = ""
            file_type = ""
            
            # 在文件夹中查找PDF或PPTX文件
            pdf_files = list(folder_path.glob("*.pdf"))
            pptx_files = list(folder_path.glob("*.pptx"))
            
            if pdf_files:
                # 如果有PDF文件，提取第一个PDF的内容
                pdf_file = pdf_files[0]
                content = extract_pdf_content(pdf_file)
                file_type = "PDF"
            elif pptx_files:
                # 如果有PPTX文件，提取第一个PPTX的第二页内容
                pptx_file = pptx_files[0]
                content = extract_pptx_second_page(pptx_file)
                file_type = "PPTX"
            else:
                content = "未找到PDF或PPTX文件"
                file_type = "无"
            
            results.append({
                "文件夹名称": folder_name,
                "内容": content,
                "文件格式": file_type
            })
    
    return results

def main():
    """主函数"""
    print("开始扫描文件夹...")
    results = scan_folders_and_extract()
    
    if not results:
        print("没有找到任何文件夹或内容")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(results)
    
    # 保存到Excel文件
    output_file = "文件夹内容提取结果.xlsx"
    df.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"提取完成！结果已保存到: {output_file}")
    print(f"共处理了 {len(results)} 个文件夹")
    
    # 显示前几行结果
    print("\n前几行结果预览:")
    print(df.head())

if __name__ == "__main__":
    main()
